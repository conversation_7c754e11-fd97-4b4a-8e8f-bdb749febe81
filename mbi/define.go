package mbi

import (
	"s2/modules/lobby/userops/userdata"

	"github.com/jfcwrlight/core/bi"
)

type userSnapshoot struct {
	ID        int64
	Name      string
	RegTime   int64
	Channel   string
	Date      uint32 `gorm:"column:date;type:UInt32;default:toYYYYMMDD(now())"`
	CreatedAt int64  `gorm:"column:create_at;type:UInt32;default:toUnixTimestamp(now())"`
}

func (m userSnapshoot) TableName() string {
	return TableUserSnapshoot
}

const TableUserSnapshoot = "user_snapshoot"

func UserSnapshoot(user *userdata.M) {
	bi.Log(TableUserSnapshoot, user.ID, user.Basic.Name, user.Basic.Channel, user.Basic.RegTime)
}

type spinResult struct {
	EventID     uint64 // 事件id
	UserID      int64
	GameID      string
	Platfrom    string
	Private     string
	Mode        int32
	SN          int64
	Height      int64  // 区块高度
	BlockHash   string // 区块hash后10位
	AssetID     int32
	Input       float64
	Output      float64
	ExtInfo     string
	Channel     string
	SeatId      int32
	Balance     float64
	ParentID    int64
	AccountName string //用户名 ｜ 也包含第三方渠道的唯一id
	Date        uint32 `gorm:"column:date;type:UInt32;default:toYYYYMMDD(now())"`
	CreatedAt   int64  `gorm:"column:create_at;type:UInt32;default:toUnixTimestamp(now())"`
}

func (m spinResult) TableName() string {
	return TableSpinResult
}

const TableSpinResult = "spin_result"

type assetChange struct {
	EventID         uint64 // 关联事件id
	UserID          int64
	AssetID         int32
	Change          float64
	ChangeLeftInput float64
	Balance         float64
	LeftInput       float64
	Cause           string
	Channel         string
	ParentID        int64
	Date            uint32 `gorm:"column:date;type:UInt32;default:toYYYYMMDD(now())"`
	CreatedAt       int64  `gorm:"column:create_at;type:UInt32;default:toUnixTimestamp(now())"`
}

func (m assetChange) TableName() string {
	return TableUserAssetChange
}

const TableUserAssetChange = "asset_change"

type userRegister struct {
	UserID    int64
	Channel   string
	Date      uint32 `gorm:"column:date;type:UInt32;default:toYYYYMMDD(now())"`
	CreatedAt int64  `gorm:"column:create_at;type:UInt32;default:toUnixTimestamp(now())"`
}

func (m userRegister) TableName() string {
	return TableUserRegister
}

const TableUserRegister = "user_register"
