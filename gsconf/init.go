package gsconf

import (
	"time"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/conf/form"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/infra/mgdb"
)

func init() {
	table.Load(table.FromMongoDocs(mgdb.Default().Database, "configs"))
	if conf.IsDev() {
		table.AutoUpdate(time.Second * 10)
	}
	form.Load(form.FromMongoDocs(mgdb.Default().Database, "configs"))
	if conf.IsDev() {
		form.AutoUpdate(time.Second * 10)
	}
}
