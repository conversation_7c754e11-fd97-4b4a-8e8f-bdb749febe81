package gsconf

import (
	"s2/pb"

	"github.com/jfcwrlight/core/conf/basic"
	"github.com/jfcwrlight/core/conf/table"
)

var (
	_ = table.Add(parseGameInfo)
)

type GameInfoConf struct {
	ID           int32
	Name         string
	Platform     string
	DeskNumber   int32
	Open         bool
	GameType     int32
	Sort         int32
	JackPotOpen  int32
	JackpotStyle []float64
	HashStyle    []float64
	Third        []any
	AlgoMode     []int32
	Tax          float64
	ListeningAPI []string
	GameUrl      string
}

func (t GameInfoConf) GetID() int32 {
	return t.ID
}

func (t GameInfoConf) TableName() string {
	return "GameInfo"
}

func (t GameInfoConf) ToPB() *pb.GameInfo {
	return &pb.GameInfo{
		ID:           t.ID,
		Name:         t.Name,
		Platform:     t.Platform,
		DeskNumber:   t.<PERSON>,
		GameType:     t.GameType,
		Open:         t.Open,
		JackPotOpen:  t.<PERSON><PERSON>,
		JackpotStyle: t.Jack<PERSON>tyle,
		HashStyle:    t.HashStyle,
	}
}

func parseGameInfo(raw basic.Raw) (*GameInfoConf, error) {
	raw.TranslateJSON("Third")
	return table.Default[GameInfoConf](raw)
}
