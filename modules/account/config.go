package account

import (
	"s2/pb"

	"github.com/jfcwrlight/core/conf"
)

func onConfigReq(body *pb.ConfigReq, response func(*pb.ConfigResp, error)) {
	jackpotPopUrl := conf.Str("clientconfig.jackpotPopUrl", "")
	verfiyUrl := conf.Str("clientconfig.verfiyUrl", "")
	hashUrl := conf.Str("clientconfig.hashUrl", "")
	verfiyApiUrl := conf.Str("clientconfig.verfiyApiUrl", "")
	response(&pb.ConfigResp{Code: pb.SUCCESS, JackpotPopUrl: jackpotPopUrl, VerfiyUrl: verfiyUrl, HashUrl: hashUrl, VerfiyApiUrl: verfiyApiUrl}, nil)
}
