package statistics

import (
	"errors"
	"s2/define"
	"s2/gsconf"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"strconv"
	"time"

	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/message"
)

const (
	MaxSeatID   = 777
	seatTakeKey = "seat.take"
)

// func (uc *useCase) GetStatscache() *common.StatsCache {
// 	sc := uc.cache.Load()
// 	return sc
// }

func (uc *useCase) Statistics(user *userdata.M, body *pb.StatisticsReq) pb.ErrCode {
	playData := body.PlayData
	user.Tag.SN++
	user.Tag.MarkDirty()
	stat := user.GetStatistics(playData.AssetID)
	stat.SN++
	stat.Input += playData.Input
	stat.Output += playData.Output
	stat.MarkDirty()
	if playData.Input > 0 {
		if user.Assets[playData.AssetID].LeftInput > 0 {
			user.Assets[playData.AssetID].LeftInput -= playData.Input
			if user.Assets[playData.AssetID].LeftInput < 0 {
				user.Assets[playData.AssetID].LeftInput = 0
			}
			user.Assets[playData.AssetID].MarkDirty()
		}
	}
	var seatId int32 = -1
	gameIdInt, err := strconv.Atoi(playData.GameId)
	if err == nil {
		/* // 推送结果验证 目前只有自己的游戏有结果校验
		if playData.Private != "" && playData.BlockHash != "" {
			userops.Notify(user, &pb.GameSpinVerifyNtf{
				GameID:      int32(gameIdInt),
				Private:     "0x" + playData.Private,
				BlockHash:   playData.BlockHash,
				BlockNumber: playData.BlockNumber,
				Seed:        playData.OracleKey,
				Payout:      float64(playData.Payout) / float64(playData.Line),
				Mode:        playData.Mode,
				CreateAt:    int64(playData.Date),
			})
		}
		*/
		gameinfo := table.Get[gsconf.GameInfoConf](gameIdInt)
		if gameinfo != nil {
			// 有桌子的游戏
			if gameinfo.DeskNumber > 0 {
				seatId = playData.SeatId
			}
		}
	}
	playData.SeatId = seatId
	// var bonus float64
	// if playData.Input > 0 && playData.Output/playData.Input > 10 {
	// 	bonus = playData.Output
	// }
	//这里后面看一下是否有必要记录到statistics服
	// UpdateData(playData.GameId, seatId, playData.Input, playData.Output, playData.Jackpot, bonus)
	if playData.Input > 0 && playData.Output > 0 { //第三方游戏的记录务必慎重，不能漏不能重复
		message.Stream.Anycast(define.ModuleName.Defi, &pb.DefiInputReq{
			Info: &pb.DefiInputInfo{
				Uid:         user.ID,
				InputUnite:  playData.Input,
				ValidInput:  playData.Input,
				ValidOutput: playData.Output,
				GameID:      playData.GameId,
			},
		})
	}
	if playData.Input > 0 {
		x := float64(int((playData.Output/playData.Input)*1000)) / 1000
		if x > 5 {
			payout := playData.Payout
			if playData.Payout == 0 {
				payout = int32(x)
			}
			winner := &pb.WinnerInfo{
				GameId:   playData.GameId,
				Platfrom: playData.Platfrom,
				UserID:   user.ID,
				UserName: strconv.Itoa(int(user.ID)), //user.Basic.Name
				Input:    playData.Input,
				Payout:   int32(payout),
				X:        x,
				Time:     time.Now().Unix(),
			}
			userops.Broadcast(&pb.WinnersAck{
				List: []*pb.WinnerInfo{winner},
			})
			uc.AddWinner(winner)
		}
	}
	playData.SN = user.Tag.SN
	playData.Channel = user.Basic.Channel
	playData.ParentID = user.Basic.ParentID
	playData.Name = user.Basic.Name
	message.Stream.Anycast(define.ModuleName.Statistics, body)
	return pb.SUCCESS
}

/*
func UpdateData(gameID string, seatId int32, input float64, output float64, jackpot float64, bonus float64) {
	pipe := rdb.Default().TxPipeline()
	ctx := context.Background()
	if input != 0 {
		// 将指定游戏ID的指定桌子的输入金额累加
		if seatId > -1 {
			gameIdInt, err := strconv.Atoi(gameID)
			if err == nil {
				pipe.HIncrByFloat(ctx, fmt.Sprintf("seats.input:%s", gameID), strconv.Itoa(int(seatId)), input)
				//增加指定座位的分数，用于排行榜排序
				pipe.ZIncrBy(ctx, todaySeatPopularRankKey(), input, uc.SeatCase().SeatTakeField(int32(gameIdInt), seatId))
				pipe.ExpireAt(ctx, todaySeatPopularRankKey(), common.UTC0().AddDate(0, 0, 3))
			}
		}
		// 将指定游戏ID的输入金额累加
		pipe.HIncrByFloat(ctx, "game.input", gameID, input)
		pipe.HIncrByFloat(ctx, "game.input", "0", input)
		//增加游戏的分数，用于排行榜排序
		pipe.ZIncrBy(ctx, todayGamePopularRankKey(), input, gameID)
		pipe.ExpireAt(ctx, todayGamePopularRankKey(), common.UTC0().AddDate(0, 0, 3))
	}
	if output != 0 {
		if seatId > -1 {
			pipe.HIncrByFloat(ctx, fmt.Sprintf("seats.output:%s", gameID), strconv.Itoa(int(seatId)), output)
		}
		pipe.HIncrByFloat(ctx, "game.output", gameID, output)
		pipe.HIncrByFloat(ctx, "game.output", "0", output)
	}
	if jackpot != 0 {
		if seatId > -1 {
			pipe.HIncrByFloat(ctx, fmt.Sprintf("seats.jackpot:%s", gameID), strconv.Itoa(int(seatId)), jackpot)
			pipe.HIncrByFloat(ctx, fmt.Sprintf("seats.jackpotcount:%s", gameID), strconv.Itoa(int(seatId)), 1)
		}
		pipe.HIncrByFloat(ctx, "game.jackpot", gameID, jackpot)
		pipe.HIncrByFloat(ctx, "game.jackpotcount", gameID, 1)
		pipe.HIncrByFloat(ctx, "game.jackpot", "0", jackpot)
		pipe.HIncrByFloat(ctx, "game.jackpotcount", "0", 1)
	}
	if bonus != 0 {
		if seatId > -1 {
			pipe.HIncrByFloat(ctx, fmt.Sprintf("seats.bonus:%s", gameID), strconv.Itoa(int(seatId)), bonus)
			pipe.HIncrByFloat(ctx, fmt.Sprintf("seats.bonuscount:%s", gameID), strconv.Itoa(int(seatId)), 1)
		}
		pipe.HIncrByFloat(ctx, "game.bonus", gameID, bonus)
		pipe.HIncrByFloat(ctx, "game.bonuscount", gameID, 1)
		pipe.HIncrByFloat(ctx, "game.bonus", "0", bonus)
		pipe.HIncrByFloat(ctx, "game.bonuscount", "0", 1)
	}
	_, err := pipe.Exec(ctx)
	if err != nil {
		log.Error(err)
	}
}
*/

func (uc *useCase) GetGameSeatRTP(gameID int32, assetID int32, seatID int32) (*pb.StatisticsGameSeatData, error) {
	seatData := uc.GetRealtimeSeatData(assetID, gameID, seatID)
	if seatData == nil {
		return nil, errors.New("seat data is nil")
	}
	return seatData, nil
}

func (uc *useCase) GetGameTotalRTP(gameID int32, assetID int32) (float64, error) {
	gameData := uc.GetRealtimeGameData(assetID, gameID)
	if gameData == nil {
		return 0, errors.New("game data is nil")
	}
	return gameData.RTP, nil
}

func (uc *useCase) GetHotGameList(limit int) []*pb.StatisticsGameData {
	return uc.GetHotGames(limit)
}

func (uc *useCase) GetHotGameSeatList(limit int) []*pb.StatisticsGameSeatData {
	rank := uc.GetHotSeats(limit)
	return rank
}

// 今日热门座位排行榜
// func todaySeatPopularRankKey() string {
// 	return uc.SeatCase().SeatPopularRankKey(time.Now().UTC())
// }

// // 今日热门游戏排行榜
// func todayGamePopularRankKey() string {
// 	return uc.GamePopularRankKey(time.Now().UTC())
// }

// // 昨日热门游戏排行榜
// func yesterdayGamePopularRankKey() string {
// 	return uc.GamePopularRankKey(common.UTC0().AddDate(0, 0, -1))
// }

// func (uc *useCase) GamePopularRankKey(t time.Time) string {
// 	return fmt.Sprintf("game.popular:%d", common.Days(t))
// }
