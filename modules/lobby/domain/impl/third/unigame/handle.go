package unigame

import (
	"math"
	"s2/define"
	"s2/gsconf"
	"s2/mbi"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"strconv"

	"github.com/jfcwrlight/core/bi"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

func (h *handle) onUnigameResultSyncMsg(user *userdata.M, body *pb.UnigameResultSyncMsg) {
	for _, item := range body.Result {
		AssetID := item.Currency
		if item.Input <= 0 && item.Output <= 0 {
			log.Errorf("user %d sync result faild, %v %v", body.UserID, body, h.AssetCase().Balance(user, AssetID))
			continue
		}
		// exchange := table.Get[gsconf.ExchangeConf](AssetID).Unigame
		// for _, item := range body.Result {
		// 	item.Input /= exchange
		// 	item.Output /= exchange
		// }
		if _, errCode := h.AssetCase().Sub(user, AssetID, item.Input, item.Cause); errCode != pb.SUCCESS {
			log.Errorf("user %d sync result faild, %v %v", body.UserID, body, h.AssetCase().Balance(user, AssetID))
			return
		}
		balance, errCode := h.AssetCase().Add(user, AssetID, item.Output, 0, item.Cause)
		if errCode != pb.SUCCESS {
			log.Errorf("user %d sync result faild, %v %v", body.UserID, body, h.AssetCase().Balance(user, AssetID))
			return
		}
		gameinfo := table.Get[gsconf.GameInfoConf](item.GameID)
		if gameinfo != nil && gameinfo.Open {
			if gameinfo.JackPotOpen > 0 {
				log.Info("unigame DefiTryJackpot GameId:", body.GameID)
				_, err := message.RequestAny[pb.DefiTryJackpotResp](define.ModuleName.Defi, &pb.DefiTryJackpotReq{
					Uid:    user.ID,
					Input:  item.Output,
					GameID: item.GameID,
				})
				if err != nil {
					log.Error("PG DefiTryJackpotReq Error:", err)
				}
			}
		}
		playData := &pb.StatisticsPlayData{
			UserID:   user.ID,
			GameId:   strconv.Itoa(int(item.GameID)),
			Platfrom: h.Name(),
			Mode:     0,
			Input:    item.Input,
			Output:   item.Output,
			AssetID:  item.Currency,
			Balance:  balance,
			SeatId:   item.SeatID,
		}
		h.StatisticsCase().Statistics(user, &pb.StatisticsReq{
			PlayData: playData,
		})
	}
}

func (h *handle) onUnigameAssetChangeReq(user *userdata.M, body *pb.UnigameAssetChangeReq, response func(*pb.UnigameAssetChangeResp, error)) {
	exchangeConf := table.Get[gsconf.ExchangeConf](body.AssetID)
	if exchangeConf == nil {
		response(&pb.UnigameAssetChangeResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	exchange := exchangeConf.Unigame
	num := body.Change / exchange
	var balance float64
	var errCode pb.ErrCode
	if num > 0 {
		balance, errCode = h.AssetCase().Add(user, body.AssetID, num, 0, body.Cause)
	} else {
		balance, errCode = h.AssetCase().Sub(user, body.AssetID, -num, body.Cause)
	}
	if errCode != pb.SUCCESS {
		response(&pb.UnigameAssetChangeResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	response(&pb.UnigameAssetChangeResp{
		Code:    errCode,
		Balance: math.Floor(exchange * balance),
	}, nil)
}

func (h *handle) onUnigameAssetBalanceReq(user *userdata.M, body *pb.UnigameAssetBalanceReq, response func(*pb.UnigameAssetBalanceResp, error)) {
	exchangeConf := table.Get[gsconf.ExchangeConf](body.AssetID)
	if exchangeConf == nil {
		response(&pb.UnigameAssetBalanceResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	response(&pb.UnigameAssetBalanceResp{
		Code:    pb.SUCCESS,
		Balance: math.Floor(exchangeConf.Unigame * h.AssetCase().Balance(user, body.AssetID)),
	}, nil)
}

func (h *handle) onUnigameResultSyncReq(user *userdata.M, body *pb.UnigameResultSyncReq, response func(*pb.UnigameResultSyncResp, error)) {
	exchangeConf := table.Get[gsconf.ExchangeConf](body.AssetID)
	if exchangeConf == nil {
		response(&pb.UnigameResultSyncResp{Code: pb.PARAM_ERROR}, nil)
		return
	}
	exchange := exchangeConf.Unigame
	result := body.Result
	result.Input /= exchange
	result.Output /= exchange
	if result.SyncAsset && result.Input > 0 {
		_, errCode := h.AssetCase().Sub(user, body.AssetID, result.Input, result.Cause)
		if errCode != pb.SUCCESS {
			response(&pb.UnigameResultSyncResp{Code: errCode}, nil)
			return
		}
	}
	if result.SyncAsset && result.Output > 0 {
		_, errCode := h.AssetCase().Add(user, body.AssetID, result.Output, 0, result.Cause)
		if errCode != pb.SUCCESS {
			response(&pb.UnigameResultSyncResp{Code: errCode}, nil)
			return
		}
	}
	bi.Log(
		mbi.TableSpinResult,
		user.EventID(),
		user.ID,
		result.Private,
		result.Public,
		result.GameID,
		0,
		body.AssetID,
		result.Input,
		result.Output,
		user.Basic.Channel,
	)
	response(&pb.UnigameResultSyncResp{
		Code:    pb.SUCCESS,
		Balance: math.Floor(h.AssetCase().Balance(user, body.AssetID) * exchange),
	}, nil)
}
