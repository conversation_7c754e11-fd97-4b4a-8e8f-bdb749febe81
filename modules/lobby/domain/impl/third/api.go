package third

import (
	"s2/gsconf"
	"s2/modules/lobby/domain/impl/third/tc"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"

	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
)

func (uc *useCase) Enter(user *userdata.M, gameID int32, currency int32) (string, pb.ErrCode) {
	conf := table.Get[gsconf.GameInfoConf](gameID)
	fw, ok := fws[conf.Platform]
	if !ok {
		return "", pb.SERVER_ERROR
	}
	url, errCode := fw.Enter(user, gameID, currency)
	if errCode != pb.SUCCESS {
		return "", errCode
	}
	return url, pb.SUCCESS
}

func (uc *useCase) Leave(user *userdata.M) (errCode pb.ErrCode) {
	if user.Third.UniGameIn.GameIn != 0 {
		gameID := user.Third.UniGameIn.GameIn
		conf := table.Get[gsconf.GameInfoConf](gameID)
		fw, ok := fws[conf.Platform]
		if !ok {
			return pb.SERVER_ERROR
		}

		errCode := fw.Leave(user, gameID)
		if errCode == pb.SUCCESS {
			user.Third.UniGameIn.GameIn = 0
			user.Third.MarkDirty()
		}
	}

	if user.Third.TCGameIn.ProductType != "" {
		errCode := tc.LeaveByArgs(user, user.Third.TCGameIn.ProductType)
		if errCode != pb.SUCCESS {
			log.Error("LeaveByArgs errCode:", errCode)
			return errCode
		}
		user.Third.TCGameIn.GameCode = ""
		user.Third.TCGameIn.ProductType = ""
		user.Third.MarkDirty()
	}
	return pb.SUCCESS
}
