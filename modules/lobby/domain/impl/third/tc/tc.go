package tc

import (
	"bytes"
	"crypto/des"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"s2/common"
	"s2/define"
	"s2/gsconf"
	"s2/modules/lobby/domain"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"

	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/utils"
	"github.com/jfcwrlight/core/utils/hs"
)

var h *handle

type handle struct {
	*domain.Domain
}

func New(d *domain.Domain) *handle {
	userops.Response(d, onTCGameListReq)
	userops.Response(d, onTCGameLaunchReq)
	h = &handle{Domain: d}
	return h
}

func (handle) Name() string {
	return "tc"
}

func (h *handle) Enter(user *userdata.M, gameID int32, currency int32) (string, pb.ErrCode) {
	return "", pb.SUCCESS
}

func (h *handle) Leave(user *userdata.M, gameID int32) pb.ErrCode {
	conf := table.Get[gsconf.GameInfoConf](gameID)
	errCode := LeaveByArgs(user, conf.Third[0])
	return errCode
}

var (
	desKey       = []byte(conf.Str("thirdTC.desKey", ""))
	signKey      = []byte(conf.Str("thirdTC.signKey", ""))
	merchantCode = conf.Str("thirdTC.merchantCode", "")
	apiURL       = conf.Str("thirdTC.apiURL", "")
)

func LeaveByArgs(user *userdata.M, productType any) pb.ErrCode {
	amount, errCode := fundTransferOut(user, productType)
	if errCode != pb.SUCCESS {
		return errCode
	}
	_, errCode = h.AssetCase().Add(user, define.AssetCoinID, amount, 0, fmt.Sprintf("tc %s leave", productType))
	return errCode
}

func username(userID int64) string {
	return fmt.Sprintf("dasino%d", userID)
}

// PKCS5填充
func pkcs5Padding(src []byte, blockSize int) []byte {
	padding := blockSize - len(src)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(src, padtext...)
}

// DES-ECB加密
func desEncryptECB(origData, key []byte) (string, error) {
	block, err := des.NewCipher(key)
	if err != nil {
		return "", err
	}

	bs := block.BlockSize()
	origData = pkcs5Padding(origData, bs)

	encrypted := make([]byte, len(origData))

	// ECB模式就是每bs长度分块独立加密
	for start := 0; start < len(origData); start += bs {
		block.Encrypt(encrypted[start:start+bs], origData[start:start+bs])
	}
	bodyTxt := base64.StdEncoding.EncodeToString(encrypted)

	return bodyTxt, nil
}

func sendEncryptedRequest[T any](body any) (*T, error) {
	// 1. 确保密钥长度为 8 字节
	if len(desKey) != 8 {
		return nil, fmt.Errorf("DES key must be 8 bytes long")
	}

	// 2. 创建 DES 加密块
	postdata, _ := json.Marshal(body)
	// postdata := []byte("{\"username\":\"dasino1\",\"currency\":\"CNY\",\"method\":\"cm\",\"password\":\"abc123456\"}")
	// 3. 填充数据使其长度为 8 的倍数
	padding := 8 - len(postdata)%8
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	postdata = append(postdata, padText...)

	// 5. 执行加密
	ciphertext, _ := desEncryptECB(postdata, desKey)
	signdata := append([]byte(ciphertext), signKey...)
	sign := sha256.Sum256(signdata)
	// bodyTxt = "ZZLvmAkcLjZ5AkFsIU3iEAe%2BbvJMcABgN0X4CHoZQPlw9MS%2BqAof4B%2BmSNXFze%2Bj9vaBf9uYTz5VonWb2jDc9mDJUMcSVSjTYikoNCxyk5k%3D"
	// 6. 生成签名（加密后的字节流与签名密钥拼接后进行 SHA-256 计算）
	signature := hex.EncodeToString(sign[:])
	data := url.Values{}
	data.Set("merchant_code", merchantCode)
	data.Set("params", ciphertext)
	data.Set("sign", signature)

	// 8. 创建 HTTP POST 请求
	dataTxt := data.Encode()
	b, err := hs.HttpRequest("POST", apiURL, []byte(dataTxt), map[string]string{
		"Content-type": "application/x-www-form-urlencoded",
	})
	if err != nil {
		log.Error(err)
		return nil, err
	}
	result := new(T)
	err = json.Unmarshal(b, result)
	if err != nil {
		return nil, err
	}
	// 11. 返回响应内容
	return result, nil
}

func createAccount(user *userdata.M) error {
	// pwd := username(user.ID)
	body := map[string]any{
		"method":   "cm",
		"username": username(user.ID),
		"password": "abc123456",
		"currency": "CNY",
	}
	result, err := sendEncryptedRequest[struct {
		Status int32  `json:"status"`
		ErrMsg string `json:"error_desc"`
	}](body)
	if err != nil {
		return err
	}
	if result.Status != 0 {
		return errors.New(result.ErrMsg)
	}
	return nil
}

func fundTransferIn(user *userdata.M, productType any, amount float64) error {
	body := map[string]any{
		"method":       "ft",
		"username":     username(user.ID),
		"product_type": productType,
		"fund_type":    1,
		"amount":       amount / define.USDExchangeRate,
		"reference_no": common.GenToken()[:40],
	}
	result, err := sendEncryptedRequest[struct {
		Status int32  `json:"status"`
		ErrMsg string `json:"error_desc"`
	}](body)
	if err != nil {
		return err
	}
	if result.Status != 0 {
		return errors.New(result.ErrMsg)
	}
	return nil
}

func fundTransferOut(user *userdata.M, productType any) (float64, pb.ErrCode) {
	body := map[string]any{
		"method":       "ftoa",
		"username":     username(user.ID),
		"product_type": productType,
		"fund_type":    1,
		"reference_no": common.GenToken()[:40],
	}
	result, err := sendEncryptedRequest[struct {
		Status int32   `json:"status"`
		ErrMsg string  `json:"error_desc"`
		Amount float64 `json:"amount"`
	}](body)
	if err != nil {
		log.Error(err)
		return 0, pb.SERVER_ERROR
	}
	if result.Status != 0 {
		log.Error(result.ErrMsg)
		return 0, pb.SERVER_ERROR
	}
	return result.Amount * define.USDExchangeRate, pb.SUCCESS
}

func launchGame(user *userdata.M, productType any, gameCode any) (string, pb.ErrCode) {
	body := map[string]any{
		"method":       "lg",
		"username":     username(user.ID),
		"nickname":     user.Basic.Name,
		"vip_level":    0,
		"product_type": productType,
		"platform":     "html5",
		"game_mode":    1, //utils.IfElse(conf.IsDev(), 0, 1),
		"game_code":    gameCode,
		"language":     utils.IfElse(user.Basic.Language == "", "EN", user.Basic.Language),
		"back_url":     "https://statics.panlaxy.io/dasinodev/",
	}
	result, err := sendEncryptedRequest[struct {
		Status int32  `json:"status"`
		ErrMsg string `json:"error_desc"`
		URL    string `json:"game_url"`
	}](body)
	if err != nil {
		log.Error(err)
		return result.URL, pb.SERVER_ERROR
	}
	if result.Status != 0 {
		log.Error(result.ErrMsg)
		return result.URL, pb.SERVER_ERROR
	}
	return result.URL, pb.SUCCESS
}
