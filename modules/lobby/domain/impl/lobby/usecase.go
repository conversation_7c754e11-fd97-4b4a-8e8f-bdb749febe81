package lobby

import (
	"s2/modules/lobby/domain"
	"s2/modules/lobby/domain/api"
	"s2/modules/lobby/userops"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/message"
)

var uc *useCase

type useCase struct {
	*domain.Domain
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.ILobby](d, domain.LobbyIndex, uc)
	userops.Response(uc, uc.onGameListReq)
	userops.Response(uc, uc.onHotGameListReq)
	userops.Response(uc, uc.onThirdAppGamePlayReq)
	userops.Response(uc, uc.onGamePlayReq)
	userops.Response(uc, uc.onUserInfoReq)
	userops.Response(uc, uc.onEnterLobbyReq)
	// Response/Handle 有回复/无回复
	// userops/message 有玩家/无玩家
	message.Handle(uc, uc.onJackpotChangeToLobbyReq)
	userops.Handle(uc, uc.onJackpotMsg)
	userops.Response(uc, uc.onGMUserBaseInfoToLobbyReq)
	userops.Response(uc, uc.onJackpotTriggerToLobbyReq)
	// userops.Handle(uc, uc.onJackpotTriggerToLobbyReq)
}
