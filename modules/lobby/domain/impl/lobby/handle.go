package lobby

import (
	"fmt"
	"net/url"
	"s2/common/cache"
	"s2/define"
	"s2/gsconf"
	"s2/modules/lobby/userops"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"sort"
	"strconv"
	"time"

	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

// 有jackpot的游戏每次spin触发 //TODO 能否改成不回复
func (uc *useCase) onJackpotTriggerToLobbyReq(user *userdata.M, body *pb.JackpotTriggerToLobbyReq, response func(*pb.JackpotTriggerToLobbyResp, error)) {
	userops.Notify(user, &pb.JackpotTriggerNtf{JackpotInfo: body.JackpotInfo})
	log.Info("JackpotTriggerNtf uId:", user.ID)
	response(&pb.JackpotTriggerToLobbyResp{Code: pb.SUCCESS}, nil)
}

// func (uc *useCase) onJackpotTriggerToLobbyReq(user *userdata.M, body *pb.JackpotTriggerToLobbyReq) {
// 	userops.Notify(user, &pb.JackpotTriggerNtf{JackpotInfo: body.JackpotInfo})
// 	log.Info("JackpotTriggerNtf uId:", user.ID)
// }

// 前端socket方式请求
func (uc *useCase) onJackpotMsg(user *userdata.M, body *pb.JackpotMsg) {
	//向defi请求数据
	resp, err := message.RequestAny[pb.DefiQueryJackpotResp](define.ModuleName.Defi, &pb.DefiQueryJackpotReq{
		UserID: user.ID,
	})
	if err != nil {
		log.Error("DefiQueryJackpotResp Error:", err)
		return
	}
	userops.Notify(user, resp.Data)
	log.Info("JackpotMsg uId:", user.ID)
}

// defi数据变化触发
func (uc *useCase) onJackpotChangeToLobbyReq(body *pb.JackpotChangeToLobbyReq) {
	userops.Broadcast(body.Data)
}

func (uc *useCase) onGameListReq(user *userdata.M, body *pb.GameListReq, response func(*pb.GameListResp, error)) {
	var list []*pb.GameInfo
	l := table.GetALL[gsconf.GameInfoConf]()
	sort.Slice(l, func(i, j int) bool {
		return l[i].Sort < l[j].Sort
	})
	for _, item := range l {
		list = append(list, item.ToPB())
	}
	response(&pb.GameListResp{
		Code: pb.SUCCESS,
		List: list,
	}, nil)
}

func (uc *useCase) onHotGameListReq(user *userdata.M, body *pb.HotGameListReq, response func(*pb.HotGameListResp, error)) {
	list := uc.StatisticsCase().GetHotGameList(100)
	if list == nil || len(list) == 0 {
		response(&pb.HotGameListResp{Code: pb.SUCCESS, List: make([]*pb.HotGameInfo, 0, 0)}, nil)
		return
	}
	resp := &pb.HotGameListResp{Code: pb.SUCCESS, List: make([]*pb.HotGameInfo, 0, len(list))}
	for _, item := range list {
		gameinfo := table.Get[gsconf.GameInfoConf](item.GameID)
		if gameinfo == nil {
			continue
		}
		resp.List = append(resp.List, &pb.HotGameInfo{
			GameID:     item.GameID,
			Platform:   gameinfo.Platform,
			DeskNumber: gameinfo.DeskNumber,
		})
	}
	response(resp, nil)
}

func (uc *useCase) onThirdAppGamePlayReq(user *userdata.M, body *pb.ThirdAppGamePlayReq, response func(*pb.GamePlayResp, error)) {
	if body.Currency == 0 {
		response(&pb.GamePlayResp{
			Code: pb.PARAM_ERROR,
		}, nil)
		return
	}
	uc.onGamePlayReq(user, &pb.GamePlayReq{
		GameID:   body.GameID,
		DeskID:   1,
		Currency: body.Currency,
		Language: body.Language,
	}, response)
}
func (uc *useCase) onGamePlayReq(user *userdata.M, body *pb.GamePlayReq, response func(*pb.GamePlayResp, error)) {
	gameID := body.GameID
	token, err := cache.UpdateOrSetUserToken(user.ID)
	if err != nil {
		response(&pb.GamePlayResp{
			Code: pb.SERVER_ERROR,
		}, nil)
		return
	}
	if body.Currency == 0 {
		lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
		body.Currency = lobbyinfo.CurrencyList[0]
	}
	gameinfo := table.Get[gsconf.GameInfoConf](body.GameID)
	if gameinfo == nil {
		response(&pb.GamePlayResp{
			Code: pb.PARAM_ERROR,
		}, nil)
		return
	}
	if !gameinfo.Open {
		response(&pb.GamePlayResp{
			Code: pb.SERVER_MAINTENANCE,
		}, nil)
		return
	}
	gameapiurl := conf.Str("gameurl.gameapiurl", "")
	recordapiurl := conf.Str("gameurl.recordapiurl", "")
	gameIdStr := strconv.Itoa(int(gameinfo.ID))
	switch gameinfo.Platform {
	case "x":
		URL := conf.Str("gameurl.xdomain", "")
		gurlname := gameinfo.GameUrl
		if gurlname != "" {
			URL += gurlname
		}
		params := url.Values{}
		params.Set("token", token)
		params.Set("gameID", gameIdStr)
		params.Set("deskID", strconv.Itoa(int(body.DeskID)))
		params.Set("apiurl", gameapiurl+"/")
		params.Set("RecordApiUrl", recordapiurl)
		params.Set("language", string(body.Language))
		params.Set("currency", strconv.Itoa(int(body.Currency)))
		args, _ := url.QueryUnescape(params.Encode())
		URL = URL + "?" + args
		response(&pb.GamePlayResp{
			Code:     pb.SUCCESS,
			URL:      URL,
			Language: body.Language,
		}, nil)
		return
	case "xp", "xking":
		xgpgamewss := conf.Str("gameurl.xgpgamewss", "")
		token = fmt.Sprintf("%s,%d,%d,%d", token, gameID, body.DeskID, body.Currency)
		nowStr := strconv.Itoa(int(time.Now().Unix()))
		URL := conf.Str("gameurl.xpgdomain", "")
		gurlname := gameinfo.GameUrl
		if gurlname != "" {
			URL += gurlname
		}
		params := url.Values{}
		params.Set("param1", "1")
		params.Set("param2", token)
		params.Set("param3", gameIdStr)
		params.Set("param4", "1")
		params.Set("param5", xgpgamewss)
		params.Set("param6", "en")
		params.Set("param8", "1")
		params.Set("apiurl", gameapiurl)
		params.Set("t", nowStr)
		args, _ := url.QueryUnescape(params.Encode())
		URL = URL + "?" + args + ""
		response(&pb.GamePlayResp{
			Code:     pb.SUCCESS,
			URL:      URL,
			Language: body.Language,
		}, nil)
		return
	case "xpp":
		token = fmt.Sprintf("%s,%d,%d,%d", token, gameID, body.DeskID, body.Currency)
		URL := conf.Str("gameurl.xpgdomain", "")
		gurlname := gameinfo.GameUrl
		if gurlname != "" {
			URL += gurlname
		}
		gameapiurl = conf.Str("gameurl.xppgameapiurl", "")
		params := url.Values{}
		params.Set("sip", gameapiurl)
		params.Set("gi", gameIdStr)
		params.Set("ot", token)
		params.Set("cid", "0")
		params.Set("ops", "luck_single_10013")
		params.Set("l", "zh")
		params.Set("oc", "0")
		params.Set("n", gameIdStr)
		params.Set("ct", "BRL")
		args, _ := url.QueryUnescape(params.Encode())
		URL = URL + "?" + args + ""
		response(&pb.GamePlayResp{
			Code:     pb.SUCCESS,
			URL:      URL,
			Language: body.Language,
		}, nil)
		return
	case "xtada":
		xgpgamewss := conf.Str("gameurl.xgpgamewss", "")
		token = fmt.Sprintf("%s,%d,%d,%d", token, gameID, body.DeskID, body.Currency)
		nowStr := strconv.Itoa(int(time.Now().Unix()))
		URL := conf.Str("gameurl.xtadadomain", "")
		gurlname := gameinfo.GameUrl
		if gurlname != "" {
			URL += gurlname
		}
		params := url.Values{}
		params.Set("param1", "1")
		params.Set("param2", token)
		params.Set("param3", gameIdStr)
		params.Set("param4", "1")
		params.Set("param5", xgpgamewss)
		params.Set("param6", "en")
		params.Set("param8", "1")
		params.Set("apiurl", gameapiurl)
		params.Set("t", nowStr)
		args, _ := url.QueryUnescape(params.Encode())
		URL = URL + "?" + args + ""

		response(&pb.GamePlayResp{
			Code:     pb.SUCCESS,
			URL:      URL,
			Language: body.Language,
		}, nil)
		return
	case "unigame":
		url, errCode := uc.ThirdCase().Enter(user, gameID, body.Currency)
		if errCode != pb.SUCCESS {
			response(&pb.GamePlayResp{
				Code: errCode,
			}, nil)
			return
		}
		url += "&language=" + string(body.Language)
		url += "&currency=" + strconv.Itoa(int(body.Currency))
		response(&pb.GamePlayResp{
			Code:     pb.SUCCESS,
			URL:      url,
			Language: body.Language,
		}, nil)
		return
	case "xbta":
		token = fmt.Sprintf("%s,%d,%d,%d", token, gameID, body.DeskID, body.Currency)
		URL := conf.Str("gameurl.xbtadomain", "")
		gurlname := gameinfo.GameUrl
		if gurlname != "" {
			URL += fmt.Sprintf("/%s.html", gurlname)
		}
		idSuffix := strconv.Itoa(int(gameID))[1:]
		modification, _ := strconv.Atoi(idSuffix)
		gameapiurl = conf.Str("gameurl.xbtaapiurl", "")
		params := url.Values{}
		params.Set("modification", strconv.Itoa(modification))
		params.Set("sid", token)
		params.Set("strategy", "iframe")
		params.Set("apiUrl", gameapiurl)
		args, _ := url.QueryUnescape(params.Encode())
		URL = URL + "?" + args + ""
		response(&pb.GamePlayResp{
			Code:     pb.SUCCESS,
			URL:      URL,
			Language: body.Language,
		}, nil)
		return
	default:
		response(&pb.GamePlayResp{
			Code: pb.PARAM_ERROR,
		}, nil)
		return
	}
}

func (uc *useCase) onUserInfoReq(user *userdata.M, body *pb.UserInfoReq, response func(*pb.UserInfoResp, error)) {
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	response(&pb.UserInfoResp{
		Code:         pb.SUCCESS,
		UserID:       user.ID,
		CurrencyList: lobbyinfo.CurrencyList,
		Balance:      uc.AssetCase().BalanceALL(user),
	}, nil)
}

func (uc *useCase) onEnterLobbyReq(user *userdata.M, body *pb.EnterLobbyReq, response func(*pb.EnterLobbyResp, error)) {
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	errCode := uc.ThirdCase().Leave(user)
	if errCode != pb.SUCCESS {
		response(&pb.EnterLobbyResp{Code: errCode}, nil)
		return
	}
	events.Publish(define.EventEnterLobby{UserID: user.ID})
	response(&pb.EnterLobbyResp{
		Code:         pb.SUCCESS,
		CurrencyList: lobbyinfo.CurrencyList,
		UserID:       user.ID,
		Balance:      uc.AssetCase().BalanceALL(user),
	}, nil)
}

func (uc *useCase) onGMUserBaseInfoToLobbyReq(user *userdata.M, body *pb.GMUserBaseInfoToLobbyReq, response func(*pb.GMUserBaseInfoToLobbyResp, error)) {
	assets := make(map[int32]*pb.IDValFloat)
	for k, v := range user.Assets {
		assets[k] = &pb.IDValFloat{ID: int64(k), Value: v.Balance, LeftInput: v.LeftInput}
	}
	totalInput := make(map[int32]float64)
	totalOutput := make(map[int32]float64)
	totalRecharge := make(map[int32]float64)
	totalWithdraw := make(map[int32]float64)
	rechargeCount := make(map[int32]int32)
	withdrawCount := make(map[int32]int32)
	for k, v := range user.Statistics {
		totalInput[k] = v.Input
		totalOutput[k] = v.Output
		totalRecharge[k] = v.Recharge
		totalWithdraw[k] = v.Withdraw
		rechargeCount[k] = v.RechargeCount
		withdrawCount[k] = v.WithdrawCount
	}
	response(&pb.GMUserBaseInfoToLobbyResp{
		Code: pb.SUCCESS,
		Data: &pb.GMUserBaseInfo{
			UserID:        user.ID,
			Name:          user.Basic.Name,
			LoginTime:     user.Tag.LoginTime,
			LoginIP:       user.Tag.LoginIP,
			LoginDevice:   user.Tag.LoginDevice,
			Assets:        assets,
			TotalInput:    totalInput,
			TotalOutput:   totalOutput,
			TotalRecharge: totalRecharge,
			TotalWithdraw: totalWithdraw,
			RechargeCount: rechargeCount,
			WithdrawCount: withdrawCount,
		},
	}, nil)
}
