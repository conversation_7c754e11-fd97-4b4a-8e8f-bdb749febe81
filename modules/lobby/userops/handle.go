package userops

import (
	"context"
	"encoding/json"
	"errors"
	"reflect"
	"runtime/debug"
	"s2/define"
	"s2/gsconf"
	"s2/modules/lobby/userops/userdata"
	"s2/pb"
	"time"

	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/conf/table"
	"github.com/jfcwrlight/core/infra/mgdb"
	"github.com/jfcwrlight/core/infra/mgdb/orm"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
	"github.com/jfcwrlight/core/message/codec"
)

func (m *userManager) onC2SPackageReq(body *pb.C2SPackageReq, response func(*pb.C2SPackageResp, error)) {
	msg, err := codec.Decode(body.Body)
	if err != nil {
		log.Errorf("onC2SPackage decode error %s", err)
		return
	}
	conc.Go(func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("panic %v: %s", r, debug.Stack())
				response(nil, errors.New("panic"))
			}
		}()
		user, err := getOrLoadFromDB(body.UserID)
		if err != nil {
			log.Error(err)
			response(nil, err)
			return
		}
		user.ResetEventID()
		user.Tag.LoginTime = time.Now().Unix()
		user.Tag.LoginIP = body.IP
		user.Tag.LoginDevice = body.Device
		h := msgResponse[reflect.TypeOf(msg)]
		h(user, msg, func(resp any, err error) {
			b, _ := json.Marshal(resp)
			response(&pb.C2SPackageResp{Body: b}, nil)
		})
		saveDB(user)
	}, body.UserID)
}

func (m *userManager) onC2SPackageMsg(body *pb.C2SPackageMsg) {
	msg, err := codec.Decode(body.Body)
	if err != nil {
		log.Errorf("onC2SPackage decode error %s", err)
		return
	}
	//顺序单线程执行
	conc.Go(func() {
		user, err := getOrLoadFromDB(body.UserID)
		user.Tag.GateID = body.GateID
		if err != nil {
			log.Error(err)
			return
		}
		user.ResetEventID()
		h := msgHandle[reflect.TypeOf(msg)]
		h(user, msg)
		//脏数据存档
		saveDB(user)
	}, body.UserID)
}

func (m *userManager) onCreateNewUserReq(body *pb.CreateNewUserReq, response func(*pb.CreateNewUserResp, error)) {
	user := userdata.New()
	user.ID = body.UserID
	user.Basic.Name = body.AccountName
	now := time.Now().Unix()
	if body.ParentId != 0 {
		user.Basic.ParentID = body.ParentId
	}
	if body.Channel != "" {
		user.Basic.Channel = body.Channel
	}
	user.Basic.RegTime = now
	user.Tag.LoginTime = now
	lobbyinfo := table.Get[gsconf.LobbyInfoConf](1)
	var initAmount float64 = 1000000
	for _, currency := range lobbyinfo.CurrencyList {
		user.Assets[currency] = &struct {
			Balance      float64
			LeftInput    float64
			orm.DirtyTag `bson:"-"`
		}{
			Balance:   initAmount,
			LeftInput: 0,
		}
	}
	conc.Async(m, func() (bool, error) {
		_, err := mgdb.Default().Collection("userdata").InsertOne(context.Background(), user)
		return true, err
	}, func(_ bool, err error) {
		if err != nil {
			response(nil, err)
			return
		}
		response(&pb.CreateNewUserResp{Code: pb.SUCCESS, ServerID: conf.ServerID}, nil)
	})
}

func (m *userManager) onUserLoginReq(user *userdata.M, body *pb.UserLoginReq, response func(*pb.UserLoginResp, error)) {
	if user.Tag.GateID != body.GateID {
		user.Tag.GateID = body.GateID
		user.Tag.MarkDirty()
	}
	response(&pb.UserLoginResp{Code: pb.SUCCESS}, nil)
	//TODO: 可以优化这个数据可以记录到大厅内存
	resp, err := message.RequestAny[pb.DefiQueryJackpotResp](define.ModuleName.Defi, &pb.DefiQueryJackpotReq{
		UserID: user.ID,
	})
	if err != nil {
		log.Error("DefiQueryJackpotResp Error:", err)
		return
	}
	Notify(user, resp.Data)
}

func (m *userManager) onUserOfflineMsg(user *userdata.M, body *pb.UserOfflineMsg) {
	user.Tag.GateID = 0
	user.MarkDirty()
	events.Publish(define.EventUserOffline{
		UserID: user.ID,
	})
}
