package account

import (
	"crypto/md5"
	"encoding/base64"
	"errors"
	"s2/modules/gm/domain"
	"s2/modules/gm/domain/adminops"
	"s2/modules/gm/domain/api"
	"sync"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/conf"
	"github.com/jfcwrlight/core/infra/mdb"
)

var uc *useCase

type useCase struct {
	*domain.Domain
	tokenCache      sync.Map
	tokenAppIdCache sync.Map
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	domainops.Register[api.IAccount](d, domain.AccountIndex, uc)
	err := mdb.Default().Table(TableName).AutoMigrate(&admin{})
	if err != nil {
		panic(err)
	}
	checkAdminAccount()
	adminops.Handle(adminops.EnumPermission.PUBLIC, uc.onAdminLoginReq)
	adminops.Handle(adminops.EnumPermission.ADMIN, uc.onAdminAccountListReq)
	adminops.Handle(adminops.EnumPermission.ADMIN, uc.onAdminAddAccountReq)
	adminops.Handle(adminops.EnumPermission.ADMIN, uc.onAdminDeleteAccountReq)
	adminops.Handle(adminops.EnumPermission.ADMIN, uc.onAdminUpdateAccountReq)
	adminops.Handle(adminops.EnumPermission.PUBLIC, uc.onAdminUpdatePwdReq)
	adminops.Handle(adminops.EnumPermission.PUBLIC, uc.onGMPlatConfogReq)
	adminops.Handle(adminops.EnumPermission.WithdrawHistory, uc.onGMHistoryWithdrawOrderReq)
	adminops.Handle(adminops.EnumPermission.RechargeHistory, uc.onGMHistoryRechargeOrderReq)
	adminops.Handle(adminops.EnumPermission.WithdrawOrderOperation, uc.onGMWithdrawOrderOperationReq)
	adminops.Handle(adminops.EnumPermission.UserInputHistory, uc.onGMUserInputHistoryReq)
	adminops.Handle(adminops.EnumPermission.UserAssetHistory, uc.onGMAssetHistoryReq)
	adminops.Handle(adminops.EnumPermission.ChangeAsset, uc.onGMChangeAssetReq)
	adminops.Handle(adminops.EnumPermission.ChangeLeftInput, uc.onGMChangeLeftInputReq)
	adminops.Handle(adminops.EnumPermission.SetupGameOdds, uc.onGMSetupGameOddsReq)
	adminops.Handle(adminops.EnumPermission.UserBaseInfo, uc.onGMUserBaseInfoReq)
	adminops.Handle(adminops.EnumPermission.AddThirdApp, uc.onGMAddThirdAppToThirdReq)
	adminops.Handle(adminops.EnumPermission.UpdateThirdApp, uc.onGMUpdateThirdAppToThirdReq)
	adminops.Handle(adminops.EnumPermission.GetThirdApp, uc.onGMGetThirdAppToThirdReq)
}

func (uc *useCase) GetAppIDByToken(token string) (string, error) {
	appId, ok := uc.tokenAppIdCache.Load(token)
	if !ok {
		return "", errors.New("token appId not found")
	}
	channel, ok := appId.(string)
	if !ok {
		return "", errors.New("token appId not found")
	}
	return channel, nil
}
func checkAdminAccount() {
	var data []*admin
	err := mdb.Default().Table(TableName).Find(&data, "username = ?", "admin").Error
	if err != nil {
		panic(err)
	}
	if len(data) != 0 {
		return
	}
	if !conf.IsDev() && !conf.Exist("gm.adminPwd") {
		panic("admin password empty")
	}
	pwd := conf.Str("gm.adminPwd", "123456")
	sum := md5.Sum([]byte(pwd))
	hash := base64.StdEncoding.EncodeToString(sum[:])
	err = mdb.Default().Table(TableName).Create(&admin{
		Username:   "admin",
		PwdDigest:  hash,
		Permission: adminops.EnumPermission.ADMIN,
	}).Error
	if err != nil {
		panic(err)
	}
}
