package account

import (
	"s2/pb"
	"strings"
)

type admin struct {
	Username   string `gorm:"column:username;primary_key;"`
	PwdDigest  string `gorm:"column:pwd_digest;"`
	Permission string `gorm:"column:permission;"`
	AppID      string `gorm:"column:appID;"`
}

const TableName = "admin"

func (m admin) ToPB() *pb.AdminAccount {
	return &pb.AdminAccount{
		Username:   m.Username,
		Permission: strings.Split(m.Permission, ","),
	}
}
