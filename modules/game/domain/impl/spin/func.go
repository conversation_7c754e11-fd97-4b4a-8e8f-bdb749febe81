package spin

import (
	"math/rand"
	"strconv"
)

func BuildSubGameDealerInfo(curwin float64, status string, choiceIdx int, subGameInfo map[string]any) map[string]any {
	av := make([]int, 5)
	switch subGameInfo["av"].(type) {
	case []int:
		av = subGameInfo["av"].([]int)
	case []any:
		for i, v := range subGameInfo["av"].([]any) {
			av[i] = int(v.(float64))
		}
	}

	cmpCard := av[0]
	// 计算庄家牌的点数，点数从2开始（2=2, 3=3, ..., A=14）
	cmpCardValue := (cmpCard / 4) + 2

	// 生成0~51的牌，排除与cmpCard点数相同的牌（4张）
	allCards := make([]int, 0, 48)          // 52 - 4 = 48张
	cmpCardBaseID := (cmpCardValue - 2) * 4 // 相同点数的起始ID
	for i := 0; i < 52; i++ {
		// 排除与庄家牌点数相同的所有4张牌
		if i < cmpCardBaseID || i >= cmpCardBaseID+4 {
			allCards = append(allCards, i)
		}
	}

	// 分别收集比cmpCard点数大和小的牌（不包含等于）
	var bigger, smaller []int
	for _, cardID := range allCards {
		cardValue := (cardID / 4) + 2
		if cardValue > cmpCardValue {
			bigger = append(bigger, cardID)
		} else if cardValue < cmpCardValue {
			smaller = append(smaller, cardID)
		}
	}

	// 洗牌
	rand.Shuffle(len(bigger), func(i, j int) { bigger[i], bigger[j] = bigger[j], bigger[i] })
	rand.Shuffle(len(smaller), func(i, j int) { smaller[i], smaller[j] = smaller[j], smaller[i] })

	if status == "win" {
		// 赢的情况：用户选择位置的牌面点数必须大于庄家牌
		if len(bigger) > 0 {
			av[choiceIdx] = bigger[0]
			bigger = bigger[1:]
		} else {
			// 如果没有更大的牌，使用庄家牌（这种情况下游戏逻辑可能需要调整）
			av[choiceIdx] = cmpCard
		}

		// 其他位置的牌面点数应该小于庄家牌
		j := 0
		for i := 1; i < 5; i++ {
			if i == choiceIdx {
				continue
			}
			if j < len(smaller) {
				av[i] = smaller[j]
				j++
			} else {
				// 如果没有足够的更小的牌，使用庄家牌
				av[i] = cmpCard
			}
		}
		subGameInfo["curWin"] = curwin
	} else {
		// 输的情况：用户选择位置的牌面点数必须小于庄家牌
		if len(smaller) > 0 {
			av[choiceIdx] = smaller[0]
			smaller = smaller[1:]
		} else {
			// 如果没有更小的牌，使用庄家牌（这种情况下游戏逻辑可能需要调整）
			av[choiceIdx] = cmpCard
		}

		// 其他位置的牌面点数应该大于庄家牌
		j := 0
		for i := 1; i < 5; i++ {
			if i == choiceIdx {
				continue
			}
			if j < len(bigger) {
				av[i] = bigger[j]
				j++
			} else {
				// 如果没有足够的更大的牌，使用庄家牌
				av[i] = cmpCard
			}
		}
		subGameInfo["curWin"] = 0
	}
	subGameInfo["attempt"] = subGameInfo["attempt"].(float64) + 1
	subGameInfo["av"] = av
	subGameInfo["userChoice"] = strconv.Itoa(choiceIdx)
	return subGameInfo
}

func BuildSubGameRedBlackInfo(curwin float64, status string, choice string, subGameInfo map[string]any) map[string]any {
	// 生成一个随机的图标ID (0-51)
	selectedCardID := rand.Intn(52)

	if status == "win" {
		// 如果是赢的状态，需要生成符合用户选择的图标ID
		switch choice {
		case "Red":
			// 红色：红桃(heart)和方块(diamond)，花色索引为2和3
			// 随机选择红桃或方块的牌
			suit := []int{2, 3}[rand.Intn(2)] // 2=红桃, 3=方块
			rank := rand.Intn(13)             // 0-12 对应 2-A
			selectedCardID = rank*4 + suit
			subGameInfo["curWin"] = curwin * 2
		case "Black":
			// 黑色：黑桃(spade)和梅花(club)，花色索引为0和1
			// 随机选择黑桃或梅花的牌
			suit := []int{0, 1}[rand.Intn(2)] // 0=黑桃, 1=梅花
			rank := rand.Intn(13)             // 0-12 对应 2-A
			selectedCardID = rank*4 + suit
			subGameInfo["curWin"] = curwin * 2
		case "Spade":
			// 黑桃：花色索引为0
			rank := rand.Intn(13) // 0-12 对应 2-A
			selectedCardID = rank*4 + 0
			subGameInfo["curWin"] = curwin * 4
		case "Club":
			// 梅花：花色索引为1
			rank := rand.Intn(13) // 0-12 对应 2-A
			selectedCardID = rank*4 + 1
			subGameInfo["curWin"] = curwin * 4
		case "Heart":
			// 红桃：花色索引为2
			rank := rand.Intn(13) // 0-12 对应 2-A
			selectedCardID = rank*4 + 2
			subGameInfo["curWin"] = curwin * 4
		case "Diamond":
			// 方块：花色索引为3
			rank := rand.Intn(13) // 0-12 对应 2-A
			selectedCardID = rank*4 + 3
			subGameInfo["curWin"] = curwin * 4
		}
		subGameInfo["rule"] = "colorsuit"
	} else {
		// 如果是输的状态，需要生成不符合用户选择的图标ID
		var excludeCards []int

		switch choice {
		case "Red":
			// 排除红色牌（红桃和方块），只选择黑色牌
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+0) // 黑桃
				excludeCards = append(excludeCards, rank*4+1) // 梅花
			}
		case "Black":
			// 排除黑色牌（黑桃和梅花），只选择红色牌
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+2) // 红桃
				excludeCards = append(excludeCards, rank*4+3) // 方块
			}
		case "Spade":
			// 排除黑桃，选择其他花色
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+1) // 梅花
				excludeCards = append(excludeCards, rank*4+2) // 红桃
				excludeCards = append(excludeCards, rank*4+3) // 方块
			}
		case "Club":
			// 排除梅花，选择其他花色
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+0) // 黑桃
				excludeCards = append(excludeCards, rank*4+2) // 红桃
				excludeCards = append(excludeCards, rank*4+3) // 方块
			}
		case "Heart":
			// 排除红桃，选择其他花色
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+0) // 黑桃
				excludeCards = append(excludeCards, rank*4+1) // 梅花
				excludeCards = append(excludeCards, rank*4+3) // 方块
			}
		case "Diamond":
			// 排除方块，选择其他花色
			for rank := 0; rank < 13; rank++ {
				excludeCards = append(excludeCards, rank*4+0) // 黑桃
				excludeCards = append(excludeCards, rank*4+1) // 梅花
				excludeCards = append(excludeCards, rank*4+2) // 红桃
			}
		}

		// 从排除列表中随机选择一张牌
		if len(excludeCards) > 0 {
			selectedCardID = excludeCards[rand.Intn(len(excludeCards))]
		}

		subGameInfo["curWin"] = 0
		subGameInfo["rule"] = "noatt"
	}

	// 将选中的图标ID存储到subGameInfo中
	subGameInfo["av"] = []int{selectedCardID}
	subGameInfo["userChoice"] = choice
	subGameInfo["attempt"] = subGameInfo["attempt"].(float64) + 1

	return subGameInfo
}
