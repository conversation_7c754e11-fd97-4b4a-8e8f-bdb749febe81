package spin

import (
	"s2/common/load"
	"s2/define"
	"s2/modules/game/domain"
	"s2/modules/game/domain/api"
	"s2/modules/game/userops"
	"sync"

	"github.com/jfcwrlight/core/basic/domainops"
	"github.com/jfcwrlight/core/basic/events"
	"github.com/jfcwrlight/core/message"
)

var uc *useCase

type useCase struct {
	*domain.Domain
	hash sync.Map
}

func Init(d *domain.Domain) {
	uc = &useCase{
		Domain: d,
	}
	load.Game()
	domainops.Register[api.ISpin](d, domain.SpinIndex, uc)
	message.Handle(uc, uc.onUpdateBlockHashMsg, message.WithoutLog())

	userops.Response(uc, uc.onGameSpinReq)
	userops.Response(uc, uc.onGamePGSpinReq)
	userops.Response(uc, uc.onGameSpinConfigReq)
	userops.Response(uc, uc.onGameSpinRecordReq)
	userops.Response(uc, uc.onGameSpinRecordCountReq)
	userops.Response(uc, uc.onGameSpinRecordQueryReq)
	userops.Response(uc, uc.onGamePGSpinRecordReq)
	userops.Response(uc, uc.onGameSpinPayoutReq)
	userops.Response(uc, uc.onGamePGSummaryReq)
	message.Response(uc, uc.onGameSpinPGRecordDetailReq)
	message.Response(uc, uc.onGMSetupOddsToGameReq)

	userops.Response(uc, uc.onUserInfoReq)
	userops.Response(uc, uc.onKingGameEnterGameReq)
	userops.Response(uc, uc.onGameKingSpinReq)

	// BTA平台与小游戏
	userops.Response(uc, uc.onGameBTASpinReq)
	userops.Response(uc, uc.onSmallGameBtaReq)
	userops.Response(uc, uc.onGameBTAUserSettingReq)
	userops.Response(uc, uc.onGetUserSettingReq)
	userops.Response(uc, uc.onGetSmallGameBtaFinishReq)

	events.Subscribe(uc, uc.onServerLaunchFinish, define.SystemEvent.ServerLaunchFinish.Name())
}
