package statistics

import (
	"context"
	"fmt"
	"s2/common"
	"s2/mbi"
	"s2/pb"
	"sync"
	"time"

	"strconv"

	"github.com/jfcwrlight/core/bi"
	"github.com/jfcwrlight/core/conc"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/system"
	"github.com/jfcwrlight/core/utils"
)

// 内存缓存结构
type CachedStatistics struct {
	Date         uint32
	GameID       int32
	AssetID      int32
	Channel      string
	BetCount     int32
	TotalInput   float64
	TotalOutput  float64
	MaxBet       float64
	MinBet       float64
	TotalJackpot float64
	JackpotCount int32
	BonusCount   int32
}

// 座位统计缓存结构
type CachedSeatStatistics struct {
	Date         uint32
	GameID       int32
	SeatID       int32
	AssetID      int32
	BetCount     int32
	TotalInput   float64
	TotalOutput  float64
	MaxBet       float64
	MinBet       float64
	TotalJackpot float64
	JackpotCount int32
	BonusCount   int32
}

// 全局缓存变量
var (
	dailyStatsCache     = make(map[string]*CachedStatistics)
	seatDailyStatsCache = make(map[string]*CachedSeatStatistics)
	dailyStatsMutex     sync.RWMutex
	seatDailyStatsMutex sync.RWMutex
)

// 初始化缓存刷新定时器
func InitStatisticsCache() {
	// 每秒刷新一次缓存到数据库
	conc.Go(func() {
		defer utils.RecoverPanic()

		ticker := time.NewTicker(time.Second)
		defer ticker.Stop()

		log.Info("Statistics cache flush goroutine started")

		for {
			select {
			case <-system.RootCtx().Done():
				log.Info("Statistics cache flush goroutine received shutdown signal")
				// 最后一次刷新缓存
				func() {
					defer func() {
						if r := recover(); r != nil {
							log.Errorf("Panic during final cache flush: %v", r)
						}
					}()
					flushCacheToDatabase()
				}()
				log.Info("Statistics cache flush goroutine exiting")
				return
			case <-ticker.C:
				// 非阻塞方式刷新缓存
				func() {
					defer func() {
						if r := recover(); r != nil {
							log.Errorf("Panic during cache flush: %v", r)
						}
					}()
					flushCacheToDatabase()
				}()
			}
		}
	})
	log.Info("Statistics cache initialized")
}

// 查询游戏统计数据api
func onStatisticsGameDataReq(body *pb.StatisticsGameDataReq, response func(*pb.StatisticsGameDataResp, error)) {
	// 解析日期范围
	startDate, endDate, err := parseStatisticsDateRange(body.StartDate, body.EndDate)
	if err != nil {
		log.Errorf("Failed to parse date range: %v", err)
		response(nil, err)
		return
	}

	// 构建查询条件
	assetID := body.AssetID

	// 查询统计数据
	statistics, err := GetGameDailyStatistics(startDate, endDate, 0, assetID, body.Channel)
	if err != nil {
		log.Errorf("Failed to get game statistics: %v", err)
		response(nil, err)
		return
	}

	// 转换为pb格式
	var gameDataList []*pb.StatisticsGameData
	for _, stat := range statistics {
		rtp := 0.0
		if stat.TotalInput > 0 {
			rtp = stat.TotalOutput / stat.TotalInput
		}
		gameData := &pb.StatisticsGameData{
			Date:         time.Unix(int64(stat.Date), 0).Format("2006-01-02"),
			GameID:       stat.GameID,
			AssetID:      stat.AssetID,
			Channel:      stat.Channel,
			TotalInput:   stat.TotalInput,
			TotalOutput:  stat.TotalOutput,
			BetCount:     stat.BetCount,
			Recover:      stat.TotalInput - stat.TotalOutput,
			RTP:          rtp,
			MaxBet:       stat.MaxBet,
			MinBet:       stat.MinBet,
			AvgBet:       stat.AvgBet,
			TotalJackpot: stat.TotalJackpot,
			JackpotCount: stat.JackpotCount,
			BonusCount:   stat.BonusCount,
		}
		gameDataList = append(gameDataList, gameData)
	}

	// 返回响应
	resp := &pb.StatisticsGameDataResp{
		Code: pb.SUCCESS,
		List: gameDataList,
	}

	response(resp, nil)
}

func onStatisticsGameSeatDataReq(body *pb.StatisticsGameSeatDataReq, response func(*pb.StatisticsGameSeatDataResp, error)) {
	startDate, endDate, err := parseStatisticsDateRange(body.StartDate, body.EndDate)
	if err != nil {
		log.Errorf("Failed to parse date range: %v", err)
		response(nil, err)
		return
	}

	statistics, err := GetGameSeatDailyStatistics(startDate, endDate, 0, -1, body.AssetID)
	if err != nil {
		log.Errorf("Failed to get game seat statistics: %v", err)
		response(nil, err)
		return
	}

	var gameSeatDataList []*pb.StatisticsGameSeatData
	for _, stat := range statistics {
		gameSeatData := &pb.StatisticsGameSeatData{
			Date:         time.Unix(int64(stat.Date), 0).Format("2006-01-02"),
			GameID:       stat.GameID,
			SeatID:       stat.SeatID,
			AssetID:      stat.AssetID,
			BetCount:     stat.BetCount,
			TotalInput:   stat.TotalInput,
			TotalOutput:  stat.TotalOutput,
			Recover:      stat.TotalInput - stat.TotalOutput,
			RTP:          stat.TotalOutput / stat.TotalInput,
			MaxBet:       stat.MaxBet,
			MinBet:       stat.MinBet,
			AvgBet:       stat.AvgBet,
			TotalJackpot: stat.TotalJackpot,
			JackpotCount: stat.JackpotCount,
			BonusCount:   stat.BonusCount,
		}
		gameSeatDataList = append(gameSeatDataList, gameSeatData)
	}

	resp := &pb.StatisticsGameSeatDataResp{
		Code: pb.SUCCESS,
		List: gameSeatDataList,
	}
	response(resp, nil)
}

// parseStatisticsDateRange 解析统计日期范围
func parseStatisticsDateRange(startDateStr, endDateStr string) (int32, int32, error) {
	// 如果没有提供日期，默认查询所有
	if startDateStr == "" || endDateStr == "" {
		return 0, 0, nil
	}

	// 解析开始日期
	startTime, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid start date format: %v", err)
	}

	// 解析结束日期
	endTime, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		return 0, 0, fmt.Errorf("invalid end date format: %v", err)
	}

	startDate := common.Days(startTime)
	endDate := common.Days(endTime)

	return startDate, endDate, nil
}

// 上报游戏数据
func onStatisticsReq(body *pb.StatisticsReq) {
	playData := body.PlayData

	// 原有的BI日志记录
	blockHash := ""
	if len(playData.BlockHash) > 16 {
		blockHash = playData.BlockHash[len(playData.BlockHash)-16:]
	}
	bi.Log(
		mbi.TableSpinResult,
		playData.EventID,
		playData.UserID,
		playData.GameId,
		playData.Platfrom,
		playData.Private,
		playData.Mode,
		playData.SN,
		playData.BlockNumber,
		blockHash,
		playData.AssetID,
		playData.Input,
		playData.Output,
		common.CompressAndBase64([]byte(playData.ExtInfo)),
		playData.Channel,
		playData.SeatId,
		playData.Balance,
		playData.ParentID,
		playData.Name,
	)

	// 新增：更新内存缓存统计数据
	updateStatisticsCache(playData)
}

// updateStatisticsCache 更新内存缓存统计数据
func updateStatisticsCache(playData *pb.StatisticsPlayData) {
	if playData.Input <= 0 && playData.Output <= 0 {
		return // 只统计有效投注
	}
	// 将playData.Date转换为当天UTC零点的时间戳
	var data time.Time
	if playData.Date == 0 {
		data = time.Now().UTC()
	} else {
		data = time.Unix(int64(playData.Date), 0).UTC()
	}
	playData.Date = uint32(common.Days(data))

	gameID, err := strconv.ParseInt(playData.GameId, 10, 32)
	if err != nil {
		log.Errorf("Failed to parse game ID: %v", err)
		return
	}
	assetID := playData.AssetID
	channel := playData.Channel

	// 更新日统计缓存
	updateDailyStatsCache(playData.Date, int32(gameID), assetID, channel, playData)
	updateDailyStatsCache(0, int32(gameID), assetID, channel, playData) //历史游戏数据

	// 更新座位统计缓存
	if playData.SeatId >= 0 {
		updateSeatDailyStatsCache(playData.Date, int32(gameID), playData.SeatId, assetID, playData)
		updateSeatDailyStatsCache(0, int32(gameID), playData.SeatId, assetID, playData) //历史座位数据
	}
}

// updateDailyStatsCache 更新日统计缓存
func updateDailyStatsCache(date uint32, gameID int32, assetID int32, channel string, playData *pb.StatisticsPlayData) {
	key := fmt.Sprintf("%d-%d-%d-%s", date, gameID, assetID, channel)

	dailyStatsMutex.Lock()
	defer dailyStatsMutex.Unlock()

	cached, exists := dailyStatsCache[key]
	if !exists {
		cached = &CachedStatistics{
			Date:         date,
			GameID:       gameID,
			AssetID:      assetID,
			Channel:      channel,
			BetCount:     0,
			TotalInput:   0,
			TotalOutput:  0,
			MaxBet:       0,
			MinBet:       0,
			TotalJackpot: 0,
			JackpotCount: 0,
			BonusCount:   0,
		}
		dailyStatsCache[key] = cached
	}

	// 累加统计数据
	cached.BetCount++
	cached.TotalInput += playData.Input
	cached.TotalOutput += playData.Output
	cached.TotalJackpot += playData.Jackpot
	if playData.Jackpot > 0 {
		cached.JackpotCount++
	}
	if playData.Input > 0 && playData.Output > 0 {
		if playData.Output/playData.Input > 10 {
			cached.BonusCount++
		}
	}
	// 更新最大最小单注
	if cached.MaxBet == 0 || playData.Input > cached.MaxBet {
		cached.MaxBet = playData.Input
	}
	if cached.MinBet == 0 || playData.Input < cached.MinBet {
		cached.MinBet = playData.Input
	}
}

// updateSeatDailyStatsCache 更新座位日统计缓存
func updateSeatDailyStatsCache(date uint32, gameID int32, seatID int32, assetID int32, playData *pb.StatisticsPlayData) {
	key := fmt.Sprintf("%d-%d-%d-%d", date, gameID, seatID, assetID)

	seatDailyStatsMutex.Lock()
	defer seatDailyStatsMutex.Unlock()

	cached, exists := seatDailyStatsCache[key]
	if !exists {
		cached = &CachedSeatStatistics{
			Date:         date,
			GameID:       gameID,
			SeatID:       seatID,
			AssetID:      assetID,
			BetCount:     0,
			TotalInput:   0,
			TotalOutput:  0,
			MaxBet:       0,
			MinBet:       0,
			TotalJackpot: 0,
			JackpotCount: 0,
			BonusCount:   0,
		}
		seatDailyStatsCache[key] = cached
	}

	// 累加统计数据
	cached.BetCount++
	cached.TotalInput += playData.Input
	cached.TotalOutput += playData.Output
	cached.TotalJackpot += playData.Jackpot
	if playData.Jackpot > 0 {
		cached.JackpotCount++
	}
	if playData.Input > 0 && playData.Output > 0 {
		if playData.Output/playData.Input > 10 {
			cached.BonusCount++
		}
	}

	// 更新最大最小单注
	if cached.MaxBet == 0 || playData.Input > cached.MaxBet {
		cached.MaxBet = playData.Input
	}
	if cached.MinBet == 0 || playData.Input < cached.MinBet {
		cached.MinBet = playData.Input
	}
}

// flushCacheToDatabase 将缓存数据刷新到数据库
func flushCacheToDatabase() {
	defer utils.RecoverPanic()

	// 检查系统是否正在关闭
	select {
	case <-system.RootCtx().Done():
		log.Debug("System shutting down, performing final cache flush")
	default:
	}

	startTime := time.Now()

	// 刷新日统计缓存
	flushDailyStatsCache()

	// 刷新座位统计缓存
	flushSeatDailyStatsCache()

	duration := time.Since(startTime)
	if duration > 100*time.Millisecond {
		log.Debugf("Cache flush took longer than expected: %v", duration)
	}
}

// flushDailyStatsCache 刷新日统计缓存到数据库
func flushDailyStatsCache() {
	dailyStatsMutex.Lock()
	// 复制当前缓存并清空，避免长时间持锁
	cacheToFlush := make(map[string]*CachedStatistics)
	for k, v := range dailyStatsCache {
		cacheToFlush[k] = v
	}
	dailyStatsCache = make(map[string]*CachedStatistics)
	dailyStatsMutex.Unlock()

	if len(cacheToFlush) == 0 {
		return
	}

	log.Debugf("Flushing %d daily statistics records to database", len(cacheToFlush))

	// 批量处理数据库操作
	batchUpdateDailyStatisticsDB(cacheToFlush)
}

// flushSeatDailyStatsCache 刷新座位统计缓存到数据库
func flushSeatDailyStatsCache() {
	seatDailyStatsMutex.Lock()
	// 复制当前缓存并清空，避免长时间持锁
	cacheToFlush := make(map[string]*CachedSeatStatistics)
	for k, v := range seatDailyStatsCache {
		cacheToFlush[k] = v
	}
	seatDailyStatsCache = make(map[string]*CachedSeatStatistics)
	seatDailyStatsMutex.Unlock()

	if len(cacheToFlush) == 0 {
		return
	}

	log.Debugf("Flushing %d seat daily statistics records to database", len(cacheToFlush))

	// 批量处理数据库操作
	batchUpdateSeatDailyStatisticsDB(cacheToFlush)
}

// batchUpdateDailyStatisticsDB 批量更新日统计数据到数据库
func batchUpdateDailyStatisticsDB(cacheData map[string]*CachedStatistics) {
	if len(cacheData) == 0 {
		return
	}

	startTime := time.Now()
	batchSize := 100 // 每批处理100条记录
	recordCount := len(cacheData)

	// 将缓存数据转换为切片以便分批处理
	var records []*CachedStatistics
	for _, cached := range cacheData {
		records = append(records, cached)
	}

	// 分批处理，减少单次事务大小
	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}
		batch := records[i:end]

		err := processDailyStatisticsBatch(batch)
		if err != nil {
			log.Errorf("Failed to process daily statistics batch %d-%d: %v", i, end-1, err)
		}
	}

	duration := time.Since(startTime)
	log.Debugf("Upserted %d daily statistics records in %v (%.2f records/ms)",
		recordCount, duration, float64(recordCount)/float64(duration.Milliseconds()))
}

// processDailyStatisticsBatch 处理单批次的日统计数据
func processDailyStatisticsBatch(batch []*CachedStatistics) error {
	if len(batch) == 0 {
		return nil
	}

	// 构建批量UPSERT SQL
	valueStrings := make([]string, 0, len(batch))
	valueArgs := make([]interface{}, 0, len(batch)*13)

	for _, cached := range batch {
		avgBet := 0.0
		if cached.BetCount > 0 {
			avgBet = cached.TotalInput / float64(cached.BetCount)
		}

		valueStrings = append(valueStrings, "(?, ?, '', ?, '', '', ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())")
		valueArgs = append(valueArgs,
			cached.Date, cached.GameID, cached.AssetID, cached.Channel,
			cached.BetCount, cached.TotalInput, cached.TotalOutput,
			cached.MaxBet, cached.MinBet, avgBet,
			cached.TotalJackpot, cached.JackpotCount, cached.BonusCount,
		)
	}

	sql := `
		INSERT INTO game_daily_statistics (
			date, game_id, game_name, asset_id, asset_name, platform, channel,
			bet_count, total_input, total_output, max_bet, min_bet, avg_bet,
			total_jackpot, jackpot_count, bonus_count, created_at, updated_at
		) VALUES ` + fmt.Sprintf("%s", valueStrings[0])

	for i := 1; i < len(valueStrings); i++ {
		sql += ", " + valueStrings[i]
	}

	sql += `
		ON DUPLICATE KEY UPDATE
			bet_count = bet_count + VALUES(bet_count),
			total_input = total_input + VALUES(total_input),
			total_output = total_output + VALUES(total_output),
			max_bet = GREATEST(max_bet, VALUES(max_bet)),
			min_bet = CASE WHEN min_bet = 0 THEN VALUES(min_bet) ELSE LEAST(min_bet, VALUES(min_bet)) END,
			avg_bet = total_input / bet_count,
			total_jackpot = total_jackpot + VALUES(total_jackpot),
			jackpot_count = jackpot_count + VALUES(jackpot_count),
			bonus_count = bonus_count + VALUES(bonus_count),
			updated_at = NOW()
	`

	// 在事务中执行批量操作，添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	tx := mdb.Default().WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := tx.Exec(sql, valueArgs...).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("batch upsert failed: %v", err)
	}

	return tx.Commit().Error
}

// GetGameDailyStatistics 获取游戏日统计数据
func GetGameDailyStatistics(startDate, endDate int32, gameID int32, assetID int32, channel string) ([]*GameDailyStatistics, error) {
	var statistics []*GameDailyStatistics
	// var total int64

	query := mdb.Default().Table(GameDailyStatisticsTableName).Where("date >= ? AND date <= ?", startDate, endDate)

	if gameID > 0 {
		query = query.Where("game_id = ?", gameID)
	}
	if assetID > 0 {
		query = query.Where("asset_id = ?", assetID)
	}
	if channel != "" {
		query = query.Where("channel = ?", channel)
	}
	// 计算总数
	// err := query.Count(&total).Error
	// if err != nil {
	// 	return nil, 0, err
	// }
	// 获取分页数据
	// offset := (page - 1) * pageSize
	err := query.Order("date DESC, game_id ASC, asset_id ASC").Find(&statistics).Error
	return statistics, err
}

// GetGameSeatDailyStatistics 获取游戏座位日统计数据
func GetGameSeatDailyStatistics(startDate, endDate int32, gameID int32, seatID int32, assetID int32) ([]*GameSeatDailyStatistics, error) {
	var statistics []*GameSeatDailyStatistics
	// var total int64

	query := mdb.Default().Table(GameSeatDailyStatisticsTableName).Where("date >= ? AND date <= ?", startDate, endDate)

	if gameID > 0 {
		query = query.Where("game_id = ?", gameID)
	}
	if seatID >= 0 {
		query = query.Where("seat_id = ?", seatID)
	}
	if assetID > 0 {
		query = query.Where("asset_id = ?", assetID)
	}

	// 计算总数
	// err := query.Count(&total).Error
	// if err != nil {
	// 	return nil, 0, err
	// }

	// 获取分页数据
	// offset := (page - 1) * pageSize
	err := query.Order("date DESC, game_id ASC, seat_id ASC, asset_id ASC").Find(&statistics).Error

	return statistics, err
}

// batchUpdateSeatDailyStatisticsDB 批量更新座位日统计数据到数据库
func batchUpdateSeatDailyStatisticsDB(cacheData map[string]*CachedSeatStatistics) {
	if len(cacheData) == 0 {
		return
	}

	startTime := time.Now()
	batchSize := 100 // 每批处理100条记录
	recordCount := len(cacheData)

	// 将缓存数据转换为切片以便分批处理
	var records []*CachedSeatStatistics
	for _, cached := range cacheData {
		records = append(records, cached)
	}

	// 分批处理，减少单次事务大小
	for i := 0; i < len(records); i += batchSize {
		end := i + batchSize
		if end > len(records) {
			end = len(records)
		}
		batch := records[i:end]

		err := processSeatDailyStatisticsBatch(batch)
		if err != nil {
			log.Errorf("Failed to process seat daily statistics batch %d-%d: %v", i, end-1, err)
		}
	}

	duration := time.Since(startTime)
	log.Debugf("Upserted %d seat daily statistics records in %v (%.2f records/ms)",
		recordCount, duration, float64(recordCount)/float64(duration.Milliseconds()))
}

// processSeatDailyStatisticsBatch 处理单批次的座位日统计数据
func processSeatDailyStatisticsBatch(batch []*CachedSeatStatistics) error {
	if len(batch) == 0 {
		return nil
	}

	// 构建批量UPSERT SQL
	valueStrings := make([]string, 0, len(batch))
	valueArgs := make([]interface{}, 0, len(batch)*13)

	for _, cached := range batch {
		avgBet := 0.0
		if cached.BetCount > 0 {
			avgBet = cached.TotalInput / float64(cached.BetCount)
		}

		valueStrings = append(valueStrings, "(?, ?, '', ?, ?, '', '', ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())")
		valueArgs = append(valueArgs,
			cached.Date, cached.GameID, cached.SeatID, cached.AssetID,
			cached.BetCount, cached.TotalInput, cached.TotalOutput,
			cached.MaxBet, cached.MinBet, avgBet,
			cached.TotalJackpot, cached.JackpotCount, cached.BonusCount,
		)
	}

	sql := `
		INSERT INTO game_seat_daily_statistics (
			date, game_id, game_name, seat_id, asset_id, asset_name, platform,
			bet_count, total_input, total_output, max_bet, min_bet, avg_bet,
			total_jackpot, jackpot_count, bonus_count, created_at, updated_at
		) VALUES ` + fmt.Sprintf("%s", valueStrings[0])

	for i := 1; i < len(valueStrings); i++ {
		sql += ", " + valueStrings[i]
	}

	sql += `
		ON DUPLICATE KEY UPDATE
			bet_count = bet_count + VALUES(bet_count),
			total_input = total_input + VALUES(total_input),
			total_output = total_output + VALUES(total_output),
			max_bet = GREATEST(max_bet, VALUES(max_bet)),
			min_bet = CASE WHEN min_bet = 0 THEN VALUES(min_bet) ELSE LEAST(min_bet, VALUES(min_bet)) END,
			avg_bet = total_input / bet_count,
			total_jackpot = total_jackpot + VALUES(total_jackpot),
			jackpot_count = jackpot_count + VALUES(jackpot_count),
			bonus_count = bonus_count + VALUES(bonus_count),
			updated_at = NOW()
	`

	// 在事务中执行批量操作，添加超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	tx := mdb.Default().WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := tx.Exec(sql, valueArgs...).Error
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("batch upsert failed: %v", err)
	}

	return tx.Commit().Error
}
