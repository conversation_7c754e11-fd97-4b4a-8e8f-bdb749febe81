package statistics

import "time"

const GameDailyStatisticsTableName = "game_daily_statistics"
const GameSeatDailyStatisticsTableName = "game_seat_daily_statistics"

// GameDailyStatistics 游戏日统计数据模型
type GameDailyStatistics struct {
	ID        uint   `gorm:"column:id;primary_key;autoIncrement"`
	Date      uint32 `gorm:"column:date;not null;index:idx_date_game_asset_channel"`            // 日期 YYYYMMDD ""为汇总所有
	GameID    int32  `gorm:"column:game_id;not null;index:idx_date_game_asset_channel"`         // 游戏ID
	GameName  string `gorm:"column:game_name;type:varchar(100)"`                                // 游戏名称
	AssetID   int32  `gorm:"column:asset_id;not null;index:idx_date_game_asset_channel"`        // 币种ID
	AssetName string `gorm:"column:asset_name;type:varchar(50)"`                                // 币种名称
	Platform  string `gorm:"column:platform;type:varchar(50)"`                                  // 平台
	Channel   string `gorm:"column:channel;type:varchar(50);index:idx_date_game_asset_channel"` // 渠道

	// 统计数据
	BetCount    int32   `gorm:"column:bet_count;default:0"`                       // 下注次数
	TotalInput  float64 `gorm:"column:total_input;type:decimal(20,8);default:0"`  // 下注金额
	TotalOutput float64 `gorm:"column:total_output;type:decimal(20,8);default:0"` // 返奖金额

	// 额外统计
	MaxBet       float64 `gorm:"column:max_bet;type:decimal(20,8);default:0"`       // 最大单注
	MinBet       float64 `gorm:"column:min_bet;type:decimal(20,8);default:0"`       // 最小单注
	AvgBet       float64 `gorm:"column:avg_bet;type:decimal(20,8);default:0"`       // 平均单注
	TotalJackpot float64 `gorm:"column:total_jackpot;type:decimal(20,8);default:0"` // 总奖池贡献
	JackpotCount int32   `gorm:"column:jackpot_count;default:0"`                    // 奖池中奖次数
	BonusCount   int32   `gorm:"column:bonus_count;default:0"`                      // 奖励触发次数

	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (GameDailyStatistics) TableName() string {
	return GameDailyStatisticsTableName
}

// GameSeatDailyStatistics 游戏座位日统计数据模型
type GameSeatDailyStatistics struct {
	ID        uint   `gorm:"column:id;primary_key;autoIncrement"`
	Date      uint32 `gorm:"column:date;not null;index:idx_date_game_seat_asset"`     // 日期时间戳
	GameID    int32  `gorm:"column:game_id;not null;index:idx_date_game_seat_asset"`  // 游戏ID
	GameName  string `gorm:"column:game_name;type:varchar(100)"`                      // 游戏名称
	SeatID    int32  `gorm:"column:seat_id;not null;index:idx_date_game_seat_asset"`  // 座位ID
	AssetID   int32  `gorm:"column:asset_id;not null;index:idx_date_game_seat_asset"` // 币种ID
	AssetName string `gorm:"column:asset_name;type:varchar(50)"`                      // 币种名称
	Platform  string `gorm:"column:platform;type:varchar(50)"`                        // 平台

	// 统计数据
	BetCount    int32   `gorm:"column:bet_count;default:0"`                       // 下注次数
	TotalInput  float64 `gorm:"column:total_input;type:decimal(20,8);default:0"`  // 下注金额
	TotalOutput float64 `gorm:"column:total_output;type:decimal(20,8);default:0"` // 返奖金额

	// 额外统计
	MaxBet       float64 `gorm:"column:max_bet;type:decimal(20,8);default:0"`       // 最大单注
	MinBet       float64 `gorm:"column:min_bet;type:decimal(20,8);default:0"`       // 最小单注
	AvgBet       float64 `gorm:"column:avg_bet;type:decimal(20,8);default:0"`       // 平均单注
	TotalJackpot float64 `gorm:"column:total_jackpot;type:decimal(20,8);default:0"` // 总奖池贡献
	JackpotCount int32   `gorm:"column:jackpot_count;default:0"`                    // 奖池中奖次数
	BonusCount   int32   `gorm:"column:bonus_count;default:0"`                      // 奖励触发次数

	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (GameSeatDailyStatistics) TableName() string {
	return GameSeatDailyStatisticsTableName
}
