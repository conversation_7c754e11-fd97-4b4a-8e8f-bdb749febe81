package statistics

import (
	"s2/define"

	"github.com/jfcwrlight/core/basic"
	"github.com/jfcwrlight/core/iface"
	"github.com/jfcwrlight/core/infra/mdb"
	"github.com/jfcwrlight/core/log"
	"github.com/jfcwrlight/core/message"
)

type module struct {
	iface.IModule
}

func New() iface.IModule {
	m := &module{
		IModule: basic.NewConcurrency(),
	}
	return m
}

func (m module) Name() string {
	return define.ModuleName.Statistics
}

func (m *module) Init() error {
	message.Handle(m, onStatisticsReq)
	message.Response(m, onStatisticsGameDataReq)
	message.Response(m, onStatisticsGameSeatDataReq)

	// 初始化游戏统计表
	err := initGameStatisticsTables()
	if err != nil {
		log.Errorf("Failed to initialize game statistics tables: %v", err)
		return err
	}

	// 初始化统计缓存系统
	InitStatisticsCache()

	return nil
}

func (m *module) Run() error {
	return nil
}

func (m *module) Exit() error {
	log.Info("Statistics module exiting, performing final cache flush")

	// 执行最后一次缓存刷新
	func() {
		defer func() {
			if r := recover(); r != nil {
				log.Errorf("Panic during module exit cache flush: %v", r)
			}
		}()
		flushCacheToDatabase()
	}()

	log.Info("Statistics module cache flush completed")
	return m.IModule.Exit()
}

// initGameStatisticsTables 初始化游戏统计表
func initGameStatisticsTables() error {
	// 创建日统计表
	err := mdb.Default().AutoMigrate(&GameDailyStatistics{})
	if err != nil {
		return err
	}

	// 创建座位日统计表
	err = mdb.Default().AutoMigrate(&GameSeatDailyStatistics{})
	if err != nil {
		return err
	}

	// 创建唯一索引（忽略错误，因为可能已存在）
	mdb.Default().Exec(`
		ALTER TABLE game_daily_statistics 
		ADD UNIQUE KEY unique_date_game_asset_channel (date, game_id, asset_id, channel)
	`)

	mdb.Default().Exec(`
		ALTER TABLE game_seat_daily_statistics 
		ADD UNIQUE KEY unique_date_game_seat_asset (date, game_id, seat_id, asset_id)
	`)

	log.Info("Game statistics tables initialized successfully")
	return nil
}
